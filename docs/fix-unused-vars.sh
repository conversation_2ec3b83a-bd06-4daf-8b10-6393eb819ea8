#!/bin/bash

# Script to fix common unused variable issues

echo "🔧 Fixing unused variables and imports..."

# Fix unused request parameters in API routes
echo "Fixing unused request parameters..."

# Add underscore prefix to unused request parameters
find src/app/api -name "*.ts" -exec sed -i '' 's/export async function GET(request: NextRequest)/export async function GET(_request: NextRequest)/g' {} \;
find src/app/api -name "*.ts" -exec sed -i '' 's/export async function POST(request: NextRequest)/export async function POST(_request: NextRequest)/g' {} \;
find src/app/api -name "*.ts" -exec sed -i '' 's/export async function PUT(request: NextRequest)/export async function PUT(_request: NextRequest)/g' {} \;
find src/app/api -name "*.ts" -exec sed -i '' 's/export async function DELETE(request: NextRequest)/export async function DELETE(_request: NextRequest)/g' {} \;

# Fix specific files with unused variables
echo "Fixing specific unused variables..."

# Fix topup page unused imports
sed -i '' '/import { Button }/d' src/app/topup/page.tsx
sed -i '' '/import { Badge }/d' src/app/topup/page.tsx
sed -i '' '/CheckCircle,/d' src/app/topup/page.tsx

# Fix floating chatbot unused variables
sed -i '' 's/SyntaxHighlighterProps,//' src/components/ai/floating-chatbot.tsx
sed -i '' 's/const style = /const _style = /' src/components/ai/floating-chatbot.tsx

# Fix copy-ai dashboard unused variables
sed -i '' 's/BalanceData,//' src/components/copy-ai/copy-ai-dashboard.tsx

# Fix navbar unused variables
sed -i '' 's/const handleSignOut = /const _handleSignOut = /' src/components/navbar.tsx

# Fix network switcher unused variables
sed -i '' 's/isDevnet,//' src/components/network-switcher.tsx

# Fix amount selector unused variables
sed -i '' 's/const isValidAmount = /const _isValidAmount = /' src/components/topup/amount-selector.tsx

# Fix topup form unused imports
sed -i '' '/import { Badge }/d' src/components/topup/topup-form.tsx

# Fix unified wallet menu unused variables
sed -i '' 's/wallet,//' src/components/unified-wallet-menu.tsx
sed -i '' 's/connect,//' src/components/unified-wallet-menu.tsx

# Fix wallet connect button unused variables
sed -i '' 's/const handleConnect = /const _handleConnect = /' src/components/wallet-connect-button.tsx

# Fix useTokenUsage hook unused variables
sed -i '' 's/userId,//' src/hooks/useTokenUsage.ts
sed -i '' 's/setAnalytics,//' src/hooks/useTokenUsage.ts
sed -i '' 's/setLoading,//' src/hooks/useTokenUsage.ts

# Fix monitoring unused variables
sed -i '' 's/timeWindow,//' src/lib/monitoring/analyzed-tweets-monitor.ts

# Fix solana topup service unused imports
sed -i '' '/transferCopyTokens,/d' src/lib/solana/topup-service.ts
sed -i '' '/CONTRACTS,/d' src/lib/solana/topup-service.ts

# Fix token service unused variables
sed -i '' 's/} catch (error) {/} catch (_error) {/' src/lib/web3/token-service.ts

# Fix cache unused variables
sed -i '' 's/const key = /const _key = /' src/lib/cache/analyzed-tweets-cache.ts

echo "✅ Fixed unused variables and imports"

# Fix display name issues
echo "Fixing missing display names..."

# Add display names to components
cat > temp_background_paths.tsx << 'EOF'
'use client';

import React from 'react';

interface BackgroundPathsProps {
  className?: string;
}

const BackgroundPaths = React.memo(function BackgroundPaths({ className }: BackgroundPathsProps) {
  return (
    <svg
      className={className}
      width="100%"
      height="100%"
      viewBox="0 0 1000 1000"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="rgba(59, 130, 246, 0.1)" />
          <stop offset="100%" stopColor="rgba(147, 51, 234, 0.1)" />
        </linearGradient>
      </defs>
      <path
        d="M0,300 Q250,200 500,300 T1000,300 L1000,1000 L0,1000 Z"
        fill="url(#gradient1)"
        opacity="0.3"
      />
      <path
        d="M0,500 Q250,400 500,500 T1000,500 L1000,1000 L0,1000 Z"
        fill="url(#gradient1)"
        opacity="0.2"
      />
    </svg>
  );
});

export default BackgroundPaths;
EOF

mv temp_background_paths.tsx src/components/ui/background-paths.tsx

echo "✅ Fixed display names"

echo "🎉 All fixes applied successfully!"
