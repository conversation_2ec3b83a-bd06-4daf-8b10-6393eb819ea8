{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/dashboard", "http://localhost:3000/personality", "http://localhost:3000/copy-ai", "http://localhost:3000/search"], "startServerCommand": "bun run start", "startServerReadyPattern": "ready", "startServerReadyTimeout": 30000, "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --headless --disable-gpu --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "en-US"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.9}], "categories:pwa": ["warn", {"minScore": 0.8}], "first-contentful-paint": ["error", {"maxNumericValue": 2000}], "largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "speed-index": ["error", {"maxNumericValue": 3000}], "uses-responsive-images": "error", "offscreen-images": "error", "render-blocking-resources": "warn", "unused-css-rules": "warn", "unused-javascript": "warn", "modern-image-formats": "warn", "uses-optimized-images": "warn", "uses-text-compression": "error", "uses-rel-preconnect": "warn", "font-display": "warn", "unminified-css": "error", "unminified-javascript": "error", "viewport": "error", "document-title": "error", "meta-description": "error", "http-status-code": "error", "link-text": "error", "crawlable-anchors": "error", "is-crawlable": "error", "robots-txt": "warn", "image-alt": "error", "input-image-alt": "error", "form-field-multiple-labels": "error", "frame-title": "error", "duplicate-id-aria": "error", "duplicate-id-active": "error", "aria-allowed-attr": "error", "aria-hidden-body": "error", "aria-hidden-focus": "error", "aria-input-field-name": "error", "aria-required-attr": "error", "aria-roles": "error", "aria-valid-attr": "error", "aria-valid-attr-value": "error", "button-name": "error", "bypass": "error", "color-contrast": "error", "definition-list": "error", "dlitem": "error", "heading-order": "error", "html-has-lang": "error", "html-lang-valid": "error", "label": "error", "landmark-one-main": "error", "link-name": "error", "list": "error", "listitem": "error", "meta-refresh": "error", "meta-viewport": "error", "object-alt": "error", "tabindex": "error", "td-headers-attr": "error", "th-has-data-cells": "error", "valid-lang": "error", "video-caption": "error"}}, "upload": {"target": "temporary-public-storage"}, "server": {"port": 9001, "storage": {"storageMethod": "sql", "sqlDialect": "sqlite", "sqlDatabasePath": "./lighthouse-ci.db"}}}, "budgets": [{"path": "/*", "timings": [{"metric": "first-contentful-paint", "budget": 2000}, {"metric": "largest-contentful-paint", "budget": 3000}, {"metric": "speed-index", "budget": 3000}, {"metric": "interactive", "budget": 4000}], "resourceSizes": [{"resourceType": "script", "budget": 500}, {"resourceType": "stylesheet", "budget": 100}, {"resourceType": "image", "budget": 200}, {"resourceType": "document", "budget": 50}, {"resourceType": "font", "budget": 100}, {"resourceType": "total", "budget": 1000}], "resourceCounts": [{"resourceType": "script", "budget": 10}, {"resourceType": "stylesheet", "budget": 5}, {"resourceType": "image", "budget": 20}, {"resourceType": "font", "budget": 4}]}]}