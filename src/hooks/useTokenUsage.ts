'use client';

import { useState, useEffect, useCallback } from 'react';
import { TokenAction, TokenUsagePolicy, DEFAULT_TOKEN_COSTS } from '@/lib/web3/token-usage-types';
import { toast } from 'sonner';

interface TokenUsageHook {
  policies: TokenUsagePolicy[];
  loading: boolean;
  error: string | null;
  getActionCost: (actionType: TokenAction) => number;
  canAffordAction: (actionType: TokenAction, userBalance: number) => boolean;
  refreshPolicies: () => Promise<void>;
  formatCost: (cost: number) => string;
}

/**
 * React hook for token usage functionality
 */
export function useTokenUsage(): TokenUsageHook {
  const [policies, setPolicies] = useState<TokenUsagePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch token usage policies
  const fetchPolicies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/token-usage/policies', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch token policies`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch token policies');
      }

      setPolicies(data.policies || []);
      console.log('✅ Token usage policies loaded:', data.policies?.length || 0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('❌ Error fetching token policies:', err);
      // Don't show toast error on initial load, just log it
      if (policies.length === 0) {
        console.warn('Failed to load token policies on initial load');
      } else {
        toast.error('Failed to refresh token policies');
      }
    } finally {
      setLoading(false);
    }
  }, [policies.length]);

  // Load policies on mount with error handling
  useEffect(() => {
    // Only fetch if we're in the browser
    if (typeof window !== 'undefined') {
      fetchPolicies();
    }
  }, [fetchPolicies]);

  // Get cost for a specific action
  const getActionCost = useCallback((actionType: TokenAction): number => {
    const policy = policies.find(p => p.actionType === actionType);
    if (policy) {
      return policy.costAmount;
    }

    // Use default costs if policies haven't loaded yet
    return DEFAULT_TOKEN_COSTS[actionType] || 0;
  }, [policies]);

  // Check if user can afford an action
  const canAffordAction = useCallback((actionType: TokenAction, userBalance: number): boolean => {
    const cost = getActionCost(actionType);
    return userBalance >= cost;
  }, [getActionCost]);

  // Format cost for display
  const formatCost = useCallback((cost: number): string => {
    return `${cost.toFixed(cost < 0.1 ? 3 : 2)} $COPY`;
  }, []);

  // Refresh policies
  const refreshPolicies = useCallback(async () => {
    await fetchPolicies();
  }, [fetchPolicies]);

  return {
    policies,
    loading,
    error,
    getActionCost,
    canAffordAction,
    refreshPolicies,
    formatCost,
  };
}

/**
 * Hook for checking specific action affordability
 */
export function useActionAffordability(actionType: TokenAction, userBalance: number) {
  const { getActionCost, canAffordAction, formatCost } = useTokenUsage();

  const cost = getActionCost(actionType);
  const canAfford = canAffordAction(actionType, userBalance);
  const formattedCost = formatCost(cost);

  return {
    cost,
    canAfford,
    formattedCost,
    shortfall: canAfford ? 0 : cost - userBalance,
  };
}

/**
 * Hook for displaying action costs in UI
 */
export function useActionCostDisplay() {
  const { getActionCost, formatCost, loading } = useTokenUsage();

  const getDisplayInfo = useCallback((actionType: TokenAction) => {
    const cost = getActionCost(actionType);
    const formattedCost = formatCost(cost);
    const isFree = cost === 0;

    return {
      cost,
      formattedCost,
      isFree,
      displayText: isFree ? 'Free' : formattedCost,
      loading,
    };
  }, [getActionCost, formatCost, loading]);

  return { getDisplayInfo };
}

/**
 * Hook for token usage analytics (placeholder for future implementation)
 */
export function useTokenUsageAnalytics(_userId?: string) {
  const [analytics, _setAnalytics] = useState(null);
  const [loading, _setLoading] = useState(false);

  // TODO: Implement analytics fetching
  // This would fetch user's usage history, spending patterns, etc.

  return {
    analytics,
    loading,
    // Future methods:
    // getTotalSpent,
    // getMostUsedActions,
    // getUsageHistory,
  };
}
