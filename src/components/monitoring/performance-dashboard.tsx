'use client';

/**
 * Performance Monitoring Dashboard
 * 
 * Displays real-time performance metrics, Core Web Vitals,
 * and Lighthouse scores for the BuddyChip application.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Zap, 
  Clock, 
  Eye, 
  Gauge, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface PerformanceMetrics {
  lighthouse: {
    performance: number;
    accessibility: number;
    bestPractices: number;
    seo: number;
    pwa: number;
  };
  coreWebVitals: {
    fcp: number; // First Contentful Paint
    lcp: number; // Largest Contentful Paint
    cls: number; // Cumulative Layout Shift
    fid: number; // First Input Delay
    ttfb: number; // Time to First Byte
  };
  bundleSize: {
    total: number;
    javascript: number;
    css: number;
    images: number;
    fonts: number;
  };
  runtime: {
    memoryUsage: number;
    renderTime: number;
    hydrationTime: number;
    routeChangeTime: number;
  };
}

// Mock data - in production this would come from real monitoring
const mockMetrics: PerformanceMetrics = {
  lighthouse: {
    performance: 92,
    accessibility: 98,
    bestPractices: 95,
    seo: 94,
    pwa: 85
  },
  coreWebVitals: {
    fcp: 1200,
    lcp: 2100,
    cls: 0.05,
    fid: 45,
    ttfb: 180
  },
  bundleSize: {
    total: 850,
    javascript: 650,
    css: 45,
    images: 120,
    fonts: 35
  },
  runtime: {
    memoryUsage: 45,
    renderTime: 16.7,
    hydrationTime: 120,
    routeChangeTime: 85
  }
};

function getScoreColor(score: number): string {
  if (score >= 90) return 'text-green-600';
  if (score >= 70) return 'text-yellow-600';
  return 'text-red-600';
}

function getScoreIcon(score: number) {
  if (score >= 90) return <CheckCircle className="w-4 h-4 text-green-600" />;
  if (score >= 70) return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
  return <AlertTriangle className="w-4 h-4 text-red-600" />;
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>(mockMetrics);
  const [isLoading, setIsLoading] = useState(false);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // In production, this would fetch real metrics
      setMetrics(prev => ({
        ...prev,
        runtime: {
          ...prev.runtime,
          memoryUsage: Math.max(20, Math.min(80, prev.runtime.memoryUsage + (Math.random() - 0.5) * 5)),
          renderTime: Math.max(10, Math.min(25, prev.runtime.renderTime + (Math.random() - 0.5) * 2))
        }
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const refreshMetrics = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor BuddyChip's performance metrics and Core Web Vitals
          </p>
        </div>
        <button
          onClick={refreshMetrics}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
        >
          <Activity className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      <Tabs defaultValue="lighthouse" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="lighthouse">Lighthouse</TabsTrigger>
          <TabsTrigger value="vitals">Core Web Vitals</TabsTrigger>
          <TabsTrigger value="bundle">Bundle Analysis</TabsTrigger>
          <TabsTrigger value="runtime">Runtime Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="lighthouse" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {Object.entries(metrics.lighthouse).map(([key, score]) => (
              <Card key={key}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </CardTitle>
                  {getScoreIcon(score)}
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{score}</div>
                  <Progress value={score} className="mt-2" />
                  <p className={`text-xs mt-1 ${getScoreColor(score)}`}>
                    {score >= 90 ? 'Excellent' : score >= 70 ? 'Good' : 'Needs Improvement'}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  First Contentful Paint
                </CardTitle>
                <CardDescription>Time until first content appears</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.coreWebVitals.fcp}ms</div>
                <Badge variant={metrics.coreWebVitals.fcp <= 1800 ? 'default' : 'destructive'}>
                  {metrics.coreWebVitals.fcp <= 1800 ? 'Good' : 'Poor'}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="w-4 h-4" />
                  Largest Contentful Paint
                </CardTitle>
                <CardDescription>Time until largest content loads</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.coreWebVitals.lcp}ms</div>
                <Badge variant={metrics.coreWebVitals.lcp <= 2500 ? 'default' : 'destructive'}>
                  {metrics.coreWebVitals.lcp <= 2500 ? 'Good' : 'Poor'}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Cumulative Layout Shift
                </CardTitle>
                <CardDescription>Visual stability measure</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.coreWebVitals.cls}</div>
                <Badge variant={metrics.coreWebVitals.cls <= 0.1 ? 'default' : 'destructive'}>
                  {metrics.coreWebVitals.cls <= 0.1 ? 'Good' : 'Poor'}
                </Badge>
              </CardContent>
            </Card>
          </div>

          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              All Core Web Vitals are within acceptable ranges. Great job on optimization!
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="bundle" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Bundle Size Breakdown</CardTitle>
                <CardDescription>Total: {formatBytes(metrics.bundleSize.total * 1024)}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(metrics.bundleSize).filter(([key]) => key !== 'total').map(([type, size]) => (
                  <div key={type} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="capitalize">{type}</span>
                      <span>{formatBytes(size * 1024)}</span>
                    </div>
                    <Progress value={(size / metrics.bundleSize.total) * 100} />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Bundle Optimization</CardTitle>
                <CardDescription>Recommendations for improvement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Dynamic imports implemented</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Tree shaking optimized</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Code splitting active</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Bundle under 1MB target</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="runtime" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Memory Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.runtime.memoryUsage.toFixed(1)}MB</div>
                <Progress value={metrics.runtime.memoryUsage} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Render Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.runtime.renderTime.toFixed(1)}ms</div>
                <Badge variant={metrics.runtime.renderTime <= 16.7 ? 'default' : 'destructive'}>
                  {metrics.runtime.renderTime <= 16.7 ? '60 FPS' : 'Slow'}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Hydration Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.runtime.hydrationTime}ms</div>
                <Badge variant={metrics.runtime.hydrationTime <= 200 ? 'default' : 'destructive'}>
                  {metrics.runtime.hydrationTime <= 200 ? 'Fast' : 'Slow'}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Route Change
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.runtime.routeChangeTime}ms</div>
                <Badge variant={metrics.runtime.routeChangeTime <= 100 ? 'default' : 'destructive'}>
                  {metrics.runtime.routeChangeTime <= 100 ? 'Instant' : 'Slow'}
                </Badge>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
