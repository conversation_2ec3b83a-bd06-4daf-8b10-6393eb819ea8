'use client';

import React, { ReactNode, Suspense, useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    <span className="ml-2 text-sm text-muted-foreground">Loading wallet...</span>
  </div>
);

// Dynamic import with better error handling
const SolanaWalletProvider = dynamic(
  () => import('./solana-wallet-provider').catch((error) => {
    console.error('Failed to load SolanaWalletProvider:', error);
    // Return a fallback component
    return {
      default: ({ children }: { children: ReactNode }) => (
        <div className="text-center p-4">
          <p className="text-red-500 mb-2">Failed to load wallet provider</p>
          <p className="text-sm text-muted-foreground mb-4">
            Please refresh the page to try again
          </p>
          {children}
        </div>
      )
    };
  }),
  {
    ssr: false,
    loading: LoadingSpinner,
  }
);

interface Web3ProviderProps {
  children: ReactNode;
}

// Error boundary component for Web3 provider
class Web3ErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('Web3Provider error boundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Web3Provider error details:', { error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center p-4">
          <p className="text-red-500 mb-2">Wallet provider failed to initialize</p>
          <p className="text-sm text-muted-foreground mb-4">
            Please refresh the page to try again
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Refresh Page
          </button>
          <div className="mt-4">
            {this.props.children}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Main Web3Provider component with optimized loading and client-side only rendering
export function Web3Provider({ children }: Web3ProviderProps) {
  const [isMounted, setIsMounted] = useState(false);

  // Ensure this only runs on the client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything on the server side
  if (!isMounted) {
    return <LoadingSpinner />;
  }

  return (
    <Web3ErrorBoundary>
      <Suspense fallback={<LoadingSpinner />}>
        <SolanaWalletProvider>
          {children}
        </SolanaWalletProvider>
      </Suspense>
    </Web3ErrorBoundary>
  );
}


