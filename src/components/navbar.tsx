'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image'; // Import Next Image
import { usePathname } from 'next/navigation';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { useIntelligentPreloader, preloadRoute } from '@/lib/utils/route-preloader';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { Menu, X } from 'lucide-react';
import { UnifiedWalletMenu } from '@/components/unified-wallet-menu';
import UserPreferencesForm from '@/components/user-preferences-form'; // Import the form

export default function Navbar() {
  const pathname = usePathname();
  const supabase = createSupabaseBrowserClient();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPreferencesModalOpen, setIsPreferencesModalOpen] = useState(false); // State for modal

  // Initialize intelligent preloader
  const { trackInteraction } = useIntelligentPreloader();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsLoggedIn(!!session);
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setIsLoggedIn(!!session);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  const _handleSignOut = async () => {
    await supabase.auth.signOut();
    window.location.href = '/login';
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { name: 'Home', href: '/' },
  ];

  const authenticatedNavItems = [
    { name: 'Dashboard', href: '/dashboard' },
    { name: 'Personality', href: '/personality' },
    { name: 'Copium', href: '/copy-ai' },
    { name: 'Top Up', href: '/topup' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background border-b border-border">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/logo.png" alt="BuddyChip Logo" width={40} height={40} />
            <span className="font-bold text-xl text-foreground">BuddyChip</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            {/* Show Home button only when not logged in */}
            {!isLoggedIn && navItems.map((item) => {
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname === item.href
                      ? 'bg-primary/10 text-primary'
                      : 'text-foreground hover:bg-muted'
                  }`}
                >
                  {item.name}
                </Link>
              );
            })}

            {isLoggedIn && authenticatedNavItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  pathname === item.href
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground hover:bg-muted'
                }`}
                onMouseEnter={() => {
                  // Preload route on hover for better UX
                  preloadRoute(item.href);
                }}
                onClick={() => {
                  // Track interaction for intelligent preloading
                  trackInteraction(item.href);
                }}
              >
                {item.name}
              </Link>
            ))}

            {isLoggedIn === null ? (
              // Loading state
              <div className="h-9 w-20 bg-muted animate-pulse rounded-md"></div>
            ) : isLoggedIn ? (
              // Unified wallet menu for logged in users
              <>
                <Dialog open={isPreferencesModalOpen} onOpenChange={setIsPreferencesModalOpen}>
                  <DialogContent className="sm:max-w-[600px] max-h-[85vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>AI Reply Preferences</DialogTitle>
                      <DialogDescription>
                        Customize how the AI analyzes tweets and generates replies for you.
                        Click &quot;Save Preferences&quot; to apply your changes.
                      </DialogDescription>
                    </DialogHeader>
                    <UserPreferencesForm onSaved={() => setIsPreferencesModalOpen(false)} />
                  </DialogContent>
                </Dialog>
                <UnifiedWalletMenu
                  variant="desktop"
                  onPreferencesClick={() => setIsPreferencesModalOpen(true)}
                />
              </>
            ) : (
              // Auth buttons for logged out users
              <div className="flex items-center space-x-2">
                <Button variant="ghost" asChild>
                  <Link href="/login">Log in</Link>
                </Button>
                <Button asChild>
                  <Link href="/signup">Sign up</Link>
                </Button>
              </div>
            )}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-border bg-background">
          {/* Show Home button only when not logged in */}
          {!isLoggedIn && navItems.map((item) => {
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  pathname === item.href
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground hover:bg-muted'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            );
          })}

          {isLoggedIn && authenticatedNavItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === item.href
                  ? 'bg-primary/10 text-primary'
                  : 'text-foreground hover:bg-muted'
              }`}
              onClick={() => {
                setIsMenuOpen(false);
                // Track interaction for intelligent preloading
                trackInteraction(item.href);
              }}
              onTouchStart={() => {
                // Preload on touch for mobile
                preloadRoute(item.href);
              }}
            >
              {item.name}
            </Link>
          ))}

          {/* Unified Wallet Menu for Mobile - only show when logged in */}
          {isLoggedIn && (
            <div className="px-3 py-2">
              <UnifiedWalletMenu
                variant="mobile"
                onPreferencesClick={() => setIsPreferencesModalOpen(true)}
                className="w-full"
              />
            </div>
          )}

          {!isLoggedIn && (
            <>
              <Link
                href="/login"
                className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-muted"
                onClick={() => setIsMenuOpen(false)}
              >
                Log in
              </Link>
              <Link
                href="/signup"
                className="block px-3 py-2 rounded-md text-base font-medium bg-primary text-primary-foreground hover:bg-primary/90"
                onClick={() => setIsMenuOpen(false)}
              >
                Sign up
              </Link>
            </>
          )}
        </div>
      )}
    </header>
  );
}
