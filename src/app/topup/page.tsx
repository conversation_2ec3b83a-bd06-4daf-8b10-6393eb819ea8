'use client';

import { useState, useEffect, Suspense, lazy } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Wallet,
  ArrowUpCircle,
  History,
  AlertTriangle,
  Info
} from 'lucide-react';

// Lazy load heavy wallet-related components
const TopUpForm = lazy(() => import('@/components/topup/topup-form').then(module => ({ default: module.TopUpForm })));
const TransactionHistory = lazy(() => import('@/components/topup/transaction-history').then(module => ({ default: module.TransactionHistory })));
const AmountSelector = lazy(() => import('@/components/topup/amount-selector').then(module => ({ default: module.AmountSelector })));

// Loading skeletons
const FormSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-8 w-48" />
    <Skeleton className="h-32 w-full" />
    <Skeleton className="h-12 w-32" />
  </div>
);

const HistorySkeleton = () => (
  <div className="space-y-4">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex justify-between items-center p-4 border rounded">
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-3 w-24" />
        </div>
        <Skeleton className="h-6 w-20" />
      </div>
    ))}
  </div>
);

interface TopUpInfo {
  limits: {
    minAmount: number;
    maxAmount: number;
    feePercentage: number;
  };
  recipientAddress: string;
  predefinedAmounts: number[];
}

export default function TopUpPage() {
  const { connected, publicKey } = useWallet();
  const [topUpInfo, setTopUpInfo] = useState<TopUpInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAmount, setSelectedAmount] = useState<number | undefined>(undefined);

  // Fetch top-up configuration
  useEffect(() => {
    const fetchTopUpInfo = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/topup/validate');
        if (!response.ok) {
          throw new Error('Failed to fetch top-up information');
        }

        const data = await response.json();
        setTopUpInfo(data);
      } catch (err) {
        console.error('❌ Error fetching top-up info:', err);
        setError(err instanceof Error ? err.message : 'Failed to load top-up information');
      } finally {
        setLoading(false);
      }
    };

    if (connected) {
      fetchTopUpInfo();
    } else {
      setLoading(false);
    }
  }, [connected]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-muted rounded w-2/3 mb-8"></div>
            <div className="grid gap-6">
              <div className="h-64 bg-muted rounded"></div>
              <div className="h-64 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
                <Wallet className="h-6 w-6" />
              </div>
              <CardTitle>Connect Your Wallet</CardTitle>
              <CardDescription>
                Please connect your Solana wallet to access the top-up feature
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You need to connect your wallet to send $COPY tokens for top-up
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Top Up Your Account</h1>
          <p className="text-muted-foreground">
            Send $COPY tokens to add balance to your BuddyChip account
          </p>
        </div>

        {/* Important Notice */}
        <Alert className="mb-6">
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Testnet Notice:</strong> This top-up feature is currently running on Solana Testnet.
            You need $COPY tokens in your wallet on the testnet network to use this feature.
            The recipient address is: <code className="bg-muted px-1 rounded text-xs">ERMNECJcmgKportP8TrBBoY19zkxWc2ov8Zp2gfFemhz</code>
          </AlertDescription>
        </Alert>

        {/* Info Cards */}
        {topUpInfo && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <ArrowUpCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Min Amount</p>
                    <p className="text-lg font-bold">{topUpInfo.limits.minAmount} $COPY</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <ArrowUpCircle className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Max Amount</p>
                    <p className="text-lg font-bold">{topUpInfo.limits.maxAmount} $COPY</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium">Fee</p>
                    <p className="text-lg font-bold">{topUpInfo.limits.feePercentage}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        <Tabs defaultValue="topup" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="topup" className="flex items-center gap-2">
              <ArrowUpCircle className="h-4 w-4" />
              Top Up
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="topup" className="space-y-6">
            {topUpInfo && (
              <>
                <Suspense fallback={<FormSkeleton />}>
                  <AmountSelector
                    predefinedAmounts={topUpInfo.predefinedAmounts}
                    limits={topUpInfo.limits}
                    onAmountSelect={setSelectedAmount}
                    selectedAmount={selectedAmount}
                  />
                </Suspense>
                <Suspense fallback={<FormSkeleton />}>
                  <TopUpForm
                    topUpInfo={topUpInfo}
                    userWallet={publicKey!}
                    selectedAmount={selectedAmount}
                  />
                </Suspense>
              </>
            )}
          </TabsContent>

          <TabsContent value="history">
            <Suspense fallback={<HistorySkeleton />}>
              <TransactionHistory />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
