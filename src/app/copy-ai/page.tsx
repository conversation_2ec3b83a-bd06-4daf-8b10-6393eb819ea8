'use client';

import { Suspense, lazy } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load the heavy CopyAI dashboard
const CopyAIDashboard = lazy(() => import('@/components/copy-ai/copy-ai-dashboard').then(module => ({ default: module.CopyAIDashboard })));

// Loading skeleton for the dashboard
const DashboardSkeleton = () => (
  <div className="container mx-auto px-4 py-8">
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <Skeleton className="h-12 w-64 mx-auto" />
        <Skeleton className="h-6 w-96 mx-auto" />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Skeleton className="h-96 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    </div>
  </div>
);

export default function CopyAIPage() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <CopyAIDashboard />
    </Suspense>
  );
}
